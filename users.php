<?php 
$page_title = 'Bangladesh Unbound - Users Management';
$page_header = 'Users Management';
include 'inc/header-admin.php'; 
?>

            <!-- Breadcrumb Navigation -->
            <nav class="breadcrumb-container" aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="dashboard-admin.php">
                            <img src="assets/img/ico/ico-dashboard.svg" alt="Dashboard" class="breadcrumb-icon">
                            Dashboard
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <img src="assets/img/ico/ico-users.svg" alt="Users" class="breadcrumb-icon">
                        Users Management
                    </li>
                </ol>
            </nav>

            <!-- Users Management Header -->
            <div class="users-header">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="section-title">
                            <img src="assets/img/ico/ico-users.svg" alt="Users" class="section-icon">
                            All Users
                        </h3>
                    </div>
                    <div class="col-md-4 text-end">
                        <button class="btn btn-add-user" data-bs-toggle="modal" data-bs-target="#addUserModal">
                            <img src="assets/img/ico/ico-profile.svg" alt="Add"> Add New User
                        </button>
                    </div>
                </div>
            </div>

            <!-- Admin Users Section -->
            <div class="users-section">
                <h4 class="users-group-title">
                    <img src="assets/img/ico/ico-dashboard.svg" alt="Admin" class="group-icon">
                    Administrators
                    <span class="user-count">2</span>
                </h4>
                <div class="users-grid">
                    <div class="user-card admin-user">
                        <div class="user-avatar">
                            <img src="assets/img/avatar.jpg" alt="Nasim Ahmed" class="user-img">
                            <span class="user-status online"></span>
                        </div>
                        <div class="user-info">
                            <h5 class="user-name">Nasim Ahmed</h5>
                            <p class="user-role">Super Admin</p>
                            <p class="user-email"><EMAIL></p>
                            <p class="user-phone">+880 1721001234</p>
                            <div class="user-meta">
                                <span class="user-joined">Joined: Jan 2024</span>
                                <span class="user-last-login">Last login: 2 hours ago</span>
                            </div>
                        </div>
                        <div class="user-actions">
                            <button class="btn btn-edit-user" data-user-id="1">
                                <img src="assets/img/ico/ico-status.svg" alt="Edit">
                            </button>
                            <button class="btn btn-delete-user" data-user-id="1">
                                <img src="assets/img/ico/ico-logout.svg" alt="Delete">
                            </button>
                        </div>
                    </div>

                    <div class="user-card admin-user">
                        <div class="user-avatar">
                            <img src="assets/img/avatar.jpg" alt="Sarah Khan" class="user-img">
                            <span class="user-status offline"></span>
                        </div>
                        <div class="user-info">
                            <h5 class="user-name">Sarah Khan</h5>
                            <p class="user-role">Admin</p>
                            <p class="user-email"><EMAIL></p>
                            <p class="user-phone">+880 1721005678</p>
                            <div class="user-meta">
                                <span class="user-joined">Joined: Feb 2024</span>
                                <span class="user-last-login">Last login: 1 day ago</span>
                            </div>
                        </div>
                        <div class="user-actions">
                            <button class="btn btn-edit-user" data-user-id="2">
                                <img src="assets/img/ico/ico-status.svg" alt="Edit">
                            </button>
                            <button class="btn btn-delete-user" data-user-id="2">
                                <img src="assets/img/ico/ico-logout.svg" alt="Delete">
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Team Leaders/Guides Section -->
            <div class="users-section">
                <h4 class="users-group-title">
                    <img src="assets/img/ico/ico-ongoing.svg" alt="Guides" class="group-icon">
                    Team Leaders & Guides
                    <span class="user-count">4</span>
                </h4>
                <div class="users-grid">
                    <div class="user-card guide-user">
                        <div class="user-avatar">
                            <img src="assets/img/avatar.jpg" alt="Rahman Ahmed" class="user-img">
                            <span class="user-status online"></span>
                        </div>
                        <div class="user-info">
                            <h5 class="user-name">Rahman Ahmed</h5>
                            <p class="user-role">Senior Guide</p>
                            <p class="user-email"><EMAIL></p>
                            <p class="user-phone">+880 1721005678</p>
                            <div class="user-meta">
                                <span class="user-joined">Joined: Mar 2024</span>
                                <span class="user-last-login">Last login: 30 min ago</span>
                            </div>
                        </div>
                        <div class="user-actions">
                            <button class="btn btn-edit-user" data-user-id="3">
                                <img src="assets/img/ico/ico-status.svg" alt="Edit">
                            </button>
                            <button class="btn btn-delete-user" data-user-id="3">
                                <img src="assets/img/ico/ico-logout.svg" alt="Delete">
                            </button>
                        </div>
                    </div>

                    <div class="user-card guide-user">
                        <div class="user-avatar">
                            <img src="assets/img/avatar.jpg" alt="Ahmed Hassan" class="user-img">
                            <span class="user-status online"></span>
                        </div>
                        <div class="user-info">
                            <h5 class="user-name">Ahmed Hassan</h5>
                            <p class="user-role">Team Leader</p>
                            <p class="user-email"><EMAIL></p>
                            <p class="user-phone">+880 1721005680</p>
                            <div class="user-meta">
                                <span class="user-joined">Joined: Mar 2024</span>
                                <span class="user-last-login">Last login: 1 hour ago</span>
                            </div>
                        </div>
                        <div class="user-actions">
                            <button class="btn btn-edit-user" data-user-id="4">
                                <img src="assets/img/ico/ico-status.svg" alt="Edit">
                            </button>
                            <button class="btn btn-delete-user" data-user-id="4">
                                <img src="assets/img/ico/ico-logout.svg" alt="Delete">
                            </button>
                        </div>
                    </div>

                    <div class="user-card guide-user">
                        <div class="user-avatar">
                            <img src="assets/img/avatar.jpg" alt="Fahim Bakhtiar" class="user-img">
                            <span class="user-status offline"></span>
                        </div>
                        <div class="user-info">
                            <h5 class="user-name">Fahim Bakhtiar</h5>
                            <p class="user-role">Guide</p>
                            <p class="user-email"><EMAIL></p>
                            <p class="user-phone">+880 1721001234</p>
                            <div class="user-meta">
                                <span class="user-joined">Joined: Apr 2024</span>
                                <span class="user-last-login">Last login: 3 hours ago</span>
                            </div>
                        </div>
                        <div class="user-actions">
                            <button class="btn btn-edit-user" data-user-id="5">
                                <img src="assets/img/ico/ico-status.svg" alt="Edit">
                            </button>
                            <button class="btn btn-delete-user" data-user-id="5">
                                <img src="assets/img/ico/ico-logout.svg" alt="Delete">
                            </button>
                        </div>
                    </div>

                    <div class="user-card guide-user">
                        <div class="user-avatar">
                            <img src="assets/img/avatar.jpg" alt="Nasir Khan" class="user-img">
                            <span class="user-status online"></span>
                        </div>
                        <div class="user-info">
                            <h5 class="user-name">Nasir Khan</h5>
                            <p class="user-role">Guide</p>
                            <p class="user-email"><EMAIL></p>
                            <p class="user-phone">+880 1721009876</p>
                            <div class="user-meta">
                                <span class="user-joined">Joined: Apr 2024</span>
                                <span class="user-last-login">Last login: 45 min ago</span>
                            </div>
                        </div>
                        <div class="user-actions">
                            <button class="btn btn-edit-user" data-user-id="6">
                                <img src="assets/img/ico/ico-status.svg" alt="Edit">
                            </button>
                            <button class="btn btn-delete-user" data-user-id="6">
                                <img src="assets/img/ico/ico-logout.svg" alt="Delete">
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customers Section -->
            <div class="users-section">
                <h4 class="users-group-title">
                    <img src="assets/img/ico/ico-profile.svg" alt="Customers" class="group-icon">
                    Customers
                    <span class="user-count">6</span>
                </h4>
                <div class="users-grid">
                    <div class="user-card customer-user">
                        <div class="user-avatar">
                            <img src="assets/img/avatar.jpg" alt="Fahad Anwar" class="user-img">
                            <span class="user-status online"></span>
                        </div>
                        <div class="user-info">
                            <h5 class="user-name">Fahad Anwar</h5>
                            <p class="user-role">Premium Customer</p>
                            <p class="user-email"><EMAIL></p>
                            <p class="user-phone">+880 1721001234</p>
                            <div class="user-meta">
                                <span class="user-joined">Joined: Jan 2024</span>
                                <span class="user-bookings">5 Bookings</span>
                            </div>
                        </div>
                        <div class="user-actions">
                            <button class="btn btn-view-user" data-user-id="7">
                                <img src="assets/img/ico/ico-map.svg" alt="View">
                            </button>
                        </div>
                    </div>

                    <div class="user-card customer-user">
                        <div class="user-avatar">
                            <img src="assets/img/avatar.jpg" alt="John Smith" class="user-img">
                            <span class="user-status offline"></span>
                        </div>
                        <div class="user-info">
                            <h5 class="user-name">John Smith</h5>
                            <p class="user-role">Regular Customer</p>
                            <p class="user-email"><EMAIL></p>
                            <p class="user-phone">****** 123 4567</p>
                            <div class="user-meta">
                                <span class="user-joined">Joined: Feb 2024</span>
                                <span class="user-bookings">3 Bookings</span>
                            </div>
                        </div>
                        <div class="user-actions">
                            <button class="btn btn-view-user" data-user-id="8">
                                <img src="assets/img/ico/ico-map.svg" alt="View">
                            </button>
                        </div>
                    </div>

                    <div class="user-card customer-user">
                        <div class="user-avatar">
                            <img src="assets/img/avatar.jpg" alt="Maria Garcia" class="user-img">
                            <span class="user-status online"></span>
                        </div>
                        <div class="user-info">
                            <h5 class="user-name">Maria Garcia</h5>
                            <p class="user-role">New Customer</p>
                            <p class="user-email"><EMAIL></p>
                            <p class="user-phone">+34 666 789 012</p>
                            <div class="user-meta">
                                <span class="user-joined">Joined: May 2024</span>
                                <span class="user-bookings">1 Booking</span>
                            </div>
                        </div>
                        <div class="user-actions">
                            <button class="btn btn-view-user" data-user-id="9">
                                <img src="assets/img/ico/ico-map.svg" alt="View">
                            </button>
                        </div>
                    </div>

                    <div class="user-card customer-user">
                        <div class="user-avatar">
                            <img src="assets/img/avatar.jpg" alt="David Wilson" class="user-img">
                            <span class="user-status offline"></span>
                        </div>
                        <div class="user-info">
                            <h5 class="user-name">David Wilson</h5>
                            <p class="user-role">Regular Customer</p>
                            <p class="user-email"><EMAIL></p>
                            <p class="user-phone">+44 20 7946 0958</p>
                            <div class="user-meta">
                                <span class="user-joined">Joined: Mar 2024</span>
                                <span class="user-bookings">2 Bookings</span>
                            </div>
                        </div>
                        <div class="user-actions">
                            <button class="btn btn-view-user" data-user-id="10">
                                <img src="assets/img/ico/ico-map.svg" alt="View">
                            </button>
                        </div>
                    </div>

                    <div class="user-card customer-user">
                        <div class="user-avatar">
                            <img src="assets/img/avatar.jpg" alt="Lisa Chen" class="user-img">
                            <span class="user-status online"></span>
                        </div>
                        <div class="user-info">
                            <h5 class="user-name">Lisa Chen</h5>
                            <p class="user-role">VIP Customer</p>
                            <p class="user-email"><EMAIL></p>
                            <p class="user-phone">+86 138 0013 8000</p>
                            <div class="user-meta">
                                <span class="user-joined">Joined: Dec 2023</span>
                                <span class="user-bookings">8 Bookings</span>
                            </div>
                        </div>
                        <div class="user-actions">
                            <button class="btn btn-view-user" data-user-id="11">
                                <img src="assets/img/ico/ico-map.svg" alt="View">
                            </button>
                        </div>
                    </div>

                    <div class="user-card customer-user">
                        <div class="user-avatar">
                            <img src="assets/img/avatar.jpg" alt="Ahmed Ali" class="user-img">
                            <span class="user-status offline"></span>
                        </div>
                        <div class="user-info">
                            <h5 class="user-name">Ahmed Ali</h5>
                            <p class="user-role">Regular Customer</p>
                            <p class="user-email"><EMAIL></p>
                            <p class="user-phone">+971 50 123 4567</p>
                            <div class="user-meta">
                                <span class="user-joined">Joined: Apr 2024</span>
                                <span class="user-bookings">1 Booking</span>
                            </div>
                        </div>
                        <div class="user-actions">
                            <button class="btn btn-view-user" data-user-id="12">
                                <img src="assets/img/ico/ico-map.svg" alt="View">
                            </button>
                        </div>
                    </div>
                </div>
            </div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addUserModalLabel">Add New User</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addUserForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="userName" class="form-label">Full Name</label>
                                <input type="text" class="form-control" id="userName" placeholder="Enter full name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="userEmail" class="form-label">Email Address</label>
                                <input type="email" class="form-control" id="userEmail" placeholder="Enter email address" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="userPhone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="userPhone" placeholder="Enter phone number" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="userRole" class="form-label">User Role</label>
                                <select class="form-control admin-select" id="userRole" required>
                                    <option value="">Select Role</option>
                                    <option value="admin">Administrator</option>
                                    <option value="team-leader">Team Leader</option>
                                    <option value="guide">Guide</option>
                                    <option value="customer">Customer</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="userPassword" class="form-label">Password</label>
                                <input type="password" class="form-control" id="userPassword" placeholder="Enter password" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="userConfirmPassword" class="form-label">Confirm Password</label>
                                <input type="password" class="form-control" id="userConfirmPassword" placeholder="Confirm password" required>
                            </div>
                        </div>
                    </div>
                    <div class="form-group mb-3">
                        <label for="userAddress" class="form-label">Address (Optional)</label>
                        <textarea class="form-control" id="userAddress" rows="3" placeholder="Enter address"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveUserBtn">Add User</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add User Modal functionality
    const addUserModal = new bootstrap.Modal(document.getElementById('addUserModal'));
    const saveUserBtn = document.getElementById('saveUserBtn');
    const userForm = document.getElementById('addUserForm');

    // Handle save user
    saveUserBtn.addEventListener('click', function() {
        const name = document.getElementById('userName').value;
        const email = document.getElementById('userEmail').value;
        const phone = document.getElementById('userPhone').value;
        const role = document.getElementById('userRole').value;
        const password = document.getElementById('userPassword').value;
        const confirmPassword = document.getElementById('userConfirmPassword').value;

        // Basic validation
        if (!name || !email || !phone || !role || !password || !confirmPassword) {
            alert('Please fill in all required fields.');
            return;
        }

        if (password !== confirmPassword) {
            alert('Passwords do not match.');
            return;
        }

        // Here you would typically send the data to the server
        console.log('Adding user:', { name, email, phone, role });

        // Reset form and close modal
        userForm.reset();
        addUserModal.hide();

        // Show success message
        alert('User added successfully!');
    });

    // Reset form when modal is closed
    document.getElementById('addUserModal').addEventListener('hidden.bs.modal', function() {
        userForm.reset();
    });

    // Handle user action buttons
    document.addEventListener('click', function(e) {
        if (e.target.closest('.btn-edit-user')) {
            const userId = e.target.closest('.btn-edit-user').dataset.userId;
            alert(`Edit user ${userId} - Feature coming soon!`);
        }

        if (e.target.closest('.btn-delete-user')) {
            const userId = e.target.closest('.btn-delete-user').dataset.userId;
            if (confirm('Are you sure you want to delete this user?')) {
                alert(`Delete user ${userId} - Feature coming soon!`);
            }
        }

        if (e.target.closest('.btn-view-user')) {
            const userId = e.target.closest('.btn-view-user').dataset.userId;
            alert(`View user ${userId} details - Feature coming soon!`);
        }
    });
});
</script>

<?php include 'inc/footer.php'; ?>
