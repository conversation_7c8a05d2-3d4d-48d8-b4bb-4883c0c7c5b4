<?php 
$page_title = 'Bangladesh Unbound - Users Management';
$page_header = 'Users Management';
include 'inc/header-admin.php'; 
?>

            <!-- Breadcrumb Navigation -->
            <nav class="breadcrumb-container" aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="dashboard-admin.php">
                            <img src="assets/img/ico/ico-dashboard.svg" alt="Dashboard" class="breadcrumb-icon">
                            Dashboard
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <img src="assets/img/ico/ico-users.svg" alt="Users" class="breadcrumb-icon">
                        Users Management
                    </li>
                </ol>
            </nav>

            <!-- Users Management Header -->
            <div class="users-header">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="section-title">
                            <img src="assets/img/ico/ico-users.svg" alt="Users" class="section-icon">
                            All Users
                        </h3>
                    </div>
                    <div class="col-md-4 text-end">
                        <button class="btn btn-add-user" data-bs-toggle="modal" data-bs-target="#addUserModal">
                            <img src="assets/img/ico/ico-profile.svg" alt="Add"> Add New User
                        </button>
                    </div>
                </div>
            </div>

            <!-- Admin Users Section -->
            <div class="users-section">
                <h4 class="users-group-title">
                    <img src="assets/img/ico/ico-dashboard.svg" alt="Admin" class="group-icon">
                    Administrators
                    <span class="user-count">2</span>
                </h4>
                <div class="users-table-container">
                    <table class="table users-table">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Role</th>
                                <th>Contact</th>
                                <th>Status</th>
                                <th>Joined</th>
                                <th>Last Login</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="admin-user-row">
                                <td>
                                    <div class="user-info-cell">
                                        <div class="user-avatar-small">
                                            <img src="assets/img/avatar.jpg" alt="Nasim Ahmed" class="user-img-small">
                                            <span class="user-status-small online"></span>
                                        </div>
                                        <div class="user-details-small">
                                            <span class="user-name-small">Nasim Ahmed</span>
                                        </div>
                                    </div>
                                </td>
                                <td><span class="role-badge admin-role">Super Admin</span></td>
                                <td>
                                    <div class="contact-info">
                                        <div class="user-email-small"><EMAIL></div>
                                        <div class="user-phone-small">+880 1721001234</div>
                                    </div>
                                </td>
                                <td><span class="status-badge online-status">Online</span></td>
                                <td>Jan 2024</td>
                                <td>2 hours ago</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-edit-user-table" data-user-id="1" title="Edit">
                                            <img src="assets/img/ico/ico-status.svg" alt="Edit">
                                        </button>
                                        <button class="btn btn-delete-user-table" data-user-id="1" title="Delete">
                                            <img src="assets/img/ico/ico-logout.svg" alt="Delete">
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr class="admin-user-row">
                                <td>
                                    <div class="user-info-cell">
                                        <div class="user-avatar-small">
                                            <img src="assets/img/avatar.jpg" alt="Sarah Khan" class="user-img-small">
                                            <span class="user-status-small offline"></span>
                                        </div>
                                        <div class="user-details-small">
                                            <span class="user-name-small">Sarah Khan</span>
                                        </div>
                                    </div>
                                </td>
                                <td><span class="role-badge admin-role">Admin</span></td>
                                <td>
                                    <div class="contact-info">
                                        <div class="user-email-small"><EMAIL></div>
                                        <div class="user-phone-small">+880 1721005678</div>
                                    </div>
                                </td>
                                <td><span class="status-badge offline-status">Offline</span></td>
                                <td>Feb 2024</td>
                                <td>1 day ago</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-edit-user-table" data-user-id="2" title="Edit">
                                            <img src="assets/img/ico/ico-status.svg" alt="Edit">
                                        </button>
                                        <button class="btn btn-delete-user-table" data-user-id="2" title="Delete">
                                            <img src="assets/img/ico/ico-logout.svg" alt="Delete">
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Team Leaders/Guides Section -->
            <div class="users-section">
                <h4 class="users-group-title">
                    <img src="assets/img/ico/ico-ongoing.svg" alt="Guides" class="group-icon">
                    Team Leaders & Guides
                    <span class="user-count">4</span>
                </h4>
                <div class="users-table-container">
                    <table class="table users-table">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Role</th>
                                <th>Contact</th>
                                <th>Status</th>
                                <th>Joined</th>
                                <th>Last Login</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="guide-user-row">
                                <td>
                                    <div class="user-info-cell">
                                        <div class="user-avatar-small">
                                            <img src="assets/img/avatar.jpg" alt="Rahman Ahmed" class="user-img-small">
                                            <span class="user-status-small online"></span>
                                        </div>
                                        <div class="user-details-small">
                                            <span class="user-name-small">Rahman Ahmed</span>
                                        </div>
                                    </div>
                                </td>
                                <td><span class="role-badge guide-role">Senior Guide</span></td>
                                <td>
                                    <div class="contact-info">
                                        <div class="user-email-small"><EMAIL></div>
                                        <div class="user-phone-small">+880 1721005678</div>
                                    </div>
                                </td>
                                <td><span class="status-badge online-status">Online</span></td>
                                <td>Mar 2024</td>
                                <td>30 min ago</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-edit-user-table" data-user-id="3" title="Edit">
                                            <img src="assets/img/ico/ico-status.svg" alt="Edit">
                                        </button>
                                        <button class="btn btn-delete-user-table" data-user-id="3" title="Delete">
                                            <img src="assets/img/ico/ico-logout.svg" alt="Delete">
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr class="guide-user-row">
                                <td>
                                    <div class="user-info-cell">
                                        <div class="user-avatar-small">
                                            <img src="assets/img/avatar.jpg" alt="Ahmed Hassan" class="user-img-small">
                                            <span class="user-status-small online"></span>
                                        </div>
                                        <div class="user-details-small">
                                            <span class="user-name-small">Ahmed Hassan</span>
                                        </div>
                                    </div>
                                </td>
                                <td><span class="role-badge guide-role">Team Leader</span></td>
                                <td>
                                    <div class="contact-info">
                                        <div class="user-email-small"><EMAIL></div>
                                        <div class="user-phone-small">+880 1721005680</div>
                                    </div>
                                </td>
                                <td><span class="status-badge online-status">Online</span></td>
                                <td>Mar 2024</td>
                                <td>1 hour ago</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-edit-user-table" data-user-id="4" title="Edit">
                                            <img src="assets/img/ico/ico-status.svg" alt="Edit">
                                        </button>
                                        <button class="btn btn-delete-user-table" data-user-id="4" title="Delete">
                                            <img src="assets/img/ico/ico-logout.svg" alt="Delete">
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr class="guide-user-row">
                                <td>
                                    <div class="user-info-cell">
                                        <div class="user-avatar-small">
                                            <img src="assets/img/avatar.jpg" alt="Fahim Bakhtiar" class="user-img-small">
                                            <span class="user-status-small offline"></span>
                                        </div>
                                        <div class="user-details-small">
                                            <span class="user-name-small">Fahim Bakhtiar</span>
                                        </div>
                                    </div>
                                </td>
                                <td><span class="role-badge guide-role">Guide</span></td>
                                <td>
                                    <div class="contact-info">
                                        <div class="user-email-small"><EMAIL></div>
                                        <div class="user-phone-small">+880 1721001234</div>
                                    </div>
                                </td>
                                <td><span class="status-badge offline-status">Offline</span></td>
                                <td>Apr 2024</td>
                                <td>3 hours ago</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-edit-user-table" data-user-id="5" title="Edit">
                                            <img src="assets/img/ico/ico-status.svg" alt="Edit">
                                        </button>
                                        <button class="btn btn-delete-user-table" data-user-id="5" title="Delete">
                                            <img src="assets/img/ico/ico-logout.svg" alt="Delete">
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr class="guide-user-row">
                                <td>
                                    <div class="user-info-cell">
                                        <div class="user-avatar-small">
                                            <img src="assets/img/avatar.jpg" alt="Nasir Khan" class="user-img-small">
                                            <span class="user-status-small online"></span>
                                        </div>
                                        <div class="user-details-small">
                                            <span class="user-name-small">Nasir Khan</span>
                                        </div>
                                    </div>
                                </td>
                                <td><span class="role-badge guide-role">Guide</span></td>
                                <td>
                                    <div class="contact-info">
                                        <div class="user-email-small"><EMAIL></div>
                                        <div class="user-phone-small">+880 1721009876</div>
                                    </div>
                                </td>
                                <td><span class="status-badge online-status">Online</span></td>
                                <td>Apr 2024</td>
                                <td>45 min ago</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-edit-user-table" data-user-id="6" title="Edit">
                                            <img src="assets/img/ico/ico-status.svg" alt="Edit">
                                        </button>
                                        <button class="btn btn-delete-user-table" data-user-id="6" title="Delete">
                                            <img src="assets/img/ico/ico-logout.svg" alt="Delete">
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Customers Section -->
            <div class="users-section">
                <h4 class="users-group-title">
                    <img src="assets/img/ico/ico-profile.svg" alt="Customers" class="group-icon">
                    Customers
                    <span class="user-count">6</span>
                </h4>
                <div class="users-table-container">
                    <table class="table users-table">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Customer Type</th>
                                <th>Contact</th>
                                <th>Status</th>
                                <th>Joined</th>
                                <th>Bookings</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="customer-user-row">
                                <td>
                                    <div class="user-info-cell">
                                        <div class="user-avatar-small">
                                            <img src="assets/img/avatar.jpg" alt="Fahad Anwar" class="user-img-small">
                                            <span class="user-status-small online"></span>
                                        </div>
                                        <div class="user-details-small">
                                            <span class="user-name-small">Fahad Anwar</span>
                                        </div>
                                    </div>
                                </td>
                                <td><span class="role-badge customer-role premium">Premium Customer</span></td>
                                <td>
                                    <div class="contact-info">
                                        <div class="user-email-small"><EMAIL></div>
                                        <div class="user-phone-small">+880 1721001234</div>
                                    </div>
                                </td>
                                <td><span class="status-badge online-status">Online</span></td>
                                <td>Jan 2024</td>
                                <td><span class="booking-count">5 Bookings</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-view-user-table" data-user-id="7" title="View Details">
                                            <img src="assets/img/ico/ico-map.svg" alt="View">
                                        </button>
                                    </div>
                                </td>
                            </tr>

                            <tr class="customer-user-row">
                                <td>
                                    <div class="user-info-cell">
                                        <div class="user-avatar-small">
                                            <img src="assets/img/avatar.jpg" alt="John Smith" class="user-img-small">
                                            <span class="user-status-small offline"></span>
                                        </div>
                                        <div class="user-details-small">
                                            <span class="user-name-small">John Smith</span>
                                        </div>
                                    </div>
                                </td>
                                <td><span class="role-badge customer-role">Regular Customer</span></td>
                                <td>
                                    <div class="contact-info">
                                        <div class="user-email-small"><EMAIL></div>
                                        <div class="user-phone-small">****** 123 4567</div>
                                    </div>
                                </td>
                                <td><span class="status-badge offline-status">Offline</span></td>
                                <td>Feb 2024</td>
                                <td><span class="booking-count">3 Bookings</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-view-user-table" data-user-id="8" title="View Details">
                                            <img src="assets/img/ico/ico-map.svg" alt="View">
                                        </button>
                                    </div>
                                </td>
                            </tr>

                            <tr class="customer-user-row">
                                <td>
                                    <div class="user-info-cell">
                                        <div class="user-avatar-small">
                                            <img src="assets/img/avatar.jpg" alt="Maria Garcia" class="user-img-small">
                                            <span class="user-status-small online"></span>
                                        </div>
                                        <div class="user-details-small">
                                            <span class="user-name-small">Maria Garcia</span>
                                        </div>
                                    </div>
                                </td>
                                <td><span class="role-badge customer-role new">New Customer</span></td>
                                <td>
                                    <div class="contact-info">
                                        <div class="user-email-small"><EMAIL></div>
                                        <div class="user-phone-small">+34 666 789 012</div>
                                    </div>
                                </td>
                                <td><span class="status-badge online-status">Online</span></td>
                                <td>May 2024</td>
                                <td><span class="booking-count">1 Booking</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-view-user-table" data-user-id="9" title="View Details">
                                            <img src="assets/img/ico/ico-map.svg" alt="View">
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr class="customer-user-row">
                                <td>
                                    <div class="user-info-cell">
                                        <div class="user-avatar-small">
                                            <img src="assets/img/avatar.jpg" alt="David Wilson" class="user-img-small">
                                            <span class="user-status-small offline"></span>
                                        </div>
                                        <div class="user-details-small">
                                            <span class="user-name-small">David Wilson</span>
                                        </div>
                                    </div>
                                </td>
                                <td><span class="role-badge customer-role">Regular Customer</span></td>
                                <td>
                                    <div class="contact-info">
                                        <div class="user-email-small"><EMAIL></div>
                                        <div class="user-phone-small">+44 20 7946 0958</div>
                                    </div>
                                </td>
                                <td><span class="status-badge offline-status">Offline</span></td>
                                <td>Mar 2024</td>
                                <td><span class="booking-count">2 Bookings</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-view-user-table" data-user-id="10" title="View Details">
                                            <img src="assets/img/ico/ico-map.svg" alt="View">
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr class="customer-user-row">
                                <td>
                                    <div class="user-info-cell">
                                        <div class="user-avatar-small">
                                            <img src="assets/img/avatar.jpg" alt="Lisa Chen" class="user-img-small">
                                            <span class="user-status-small online"></span>
                                        </div>
                                        <div class="user-details-small">
                                            <span class="user-name-small">Lisa Chen</span>
                                        </div>
                                    </div>
                                </td>
                                <td><span class="role-badge customer-role vip">VIP Customer</span></td>
                                <td>
                                    <div class="contact-info">
                                        <div class="user-email-small"><EMAIL></div>
                                        <div class="user-phone-small">+86 138 0013 8000</div>
                                    </div>
                                </td>
                                <td><span class="status-badge online-status">Online</span></td>
                                <td>Dec 2023</td>
                                <td><span class="booking-count">8 Bookings</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-view-user-table" data-user-id="11" title="View Details">
                                            <img src="assets/img/ico/ico-map.svg" alt="View">
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr class="customer-user-row">
                                <td>
                                    <div class="user-info-cell">
                                        <div class="user-avatar-small">
                                            <img src="assets/img/avatar.jpg" alt="Ahmed Ali" class="user-img-small">
                                            <span class="user-status-small offline"></span>
                                        </div>
                                        <div class="user-details-small">
                                            <span class="user-name-small">Ahmed Ali</span>
                                        </div>
                                    </div>
                                </td>
                                <td><span class="role-badge customer-role">Regular Customer</span></td>
                                <td>
                                    <div class="contact-info">
                                        <div class="user-email-small"><EMAIL></div>
                                        <div class="user-phone-small">+971 50 123 4567</div>
                                    </div>
                                </td>
                                <td><span class="status-badge offline-status">Offline</span></td>
                                <td>Apr 2024</td>
                                <td><span class="booking-count">1 Booking</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-view-user-table" data-user-id="12" title="View Details">
                                            <img src="assets/img/ico/ico-map.svg" alt="View">
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addUserModalLabel">Add New User</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addUserForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="userName" class="form-label">Full Name</label>
                                <input type="text" class="form-control" id="userName" placeholder="Enter full name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="userEmail" class="form-label">Email Address</label>
                                <input type="email" class="form-control" id="userEmail" placeholder="Enter email address" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="userPhone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="userPhone" placeholder="Enter phone number" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="userRole" class="form-label">User Role</label>
                                <select class="form-control admin-select" id="userRole" required>
                                    <option value="">Select Role</option>
                                    <option value="admin">Administrator</option>
                                    <option value="team-leader">Team Leader</option>
                                    <option value="guide">Guide</option>
                                    <option value="customer">Customer</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="userPassword" class="form-label">Password</label>
                                <input type="password" class="form-control" id="userPassword" placeholder="Enter password" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="userConfirmPassword" class="form-label">Confirm Password</label>
                                <input type="password" class="form-control" id="userConfirmPassword" placeholder="Confirm password" required>
                            </div>
                        </div>
                    </div>
                    <div class="form-group mb-3">
                        <label for="userAddress" class="form-label">Address (Optional)</label>
                        <textarea class="form-control" id="userAddress" rows="3" placeholder="Enter address"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveUserBtn">Add User</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add User Modal functionality
    const addUserModal = new bootstrap.Modal(document.getElementById('addUserModal'));
    const saveUserBtn = document.getElementById('saveUserBtn');
    const userForm = document.getElementById('addUserForm');

    // Handle save user
    saveUserBtn.addEventListener('click', function() {
        const name = document.getElementById('userName').value;
        const email = document.getElementById('userEmail').value;
        const phone = document.getElementById('userPhone').value;
        const role = document.getElementById('userRole').value;
        const password = document.getElementById('userPassword').value;
        const confirmPassword = document.getElementById('userConfirmPassword').value;

        // Basic validation
        if (!name || !email || !phone || !role || !password || !confirmPassword) {
            alert('Please fill in all required fields.');
            return;
        }

        if (password !== confirmPassword) {
            alert('Passwords do not match.');
            return;
        }

        // Here you would typically send the data to the server
        console.log('Adding user:', { name, email, phone, role });

        // Reset form and close modal
        userForm.reset();
        addUserModal.hide();

        // Show success message
        alert('User added successfully!');
    });

    // Reset form when modal is closed
    document.getElementById('addUserModal').addEventListener('hidden.bs.modal', function() {
        userForm.reset();
    });

    // Handle user action buttons
    document.addEventListener('click', function(e) {
        if (e.target.closest('.btn-edit-user-table')) {
            const userId = e.target.closest('.btn-edit-user-table').dataset.userId;
            alert(`Edit user ${userId} - Feature coming soon!`);
        }

        if (e.target.closest('.btn-delete-user-table')) {
            const userId = e.target.closest('.btn-delete-user-table').dataset.userId;
            if (confirm('Are you sure you want to delete this user?')) {
                alert(`Delete user ${userId} - Feature coming soon!`);
            }
        }

        if (e.target.closest('.btn-view-user-table')) {
            const userId = e.target.closest('.btn-view-user-table').dataset.userId;
            alert(`View user ${userId} details - Feature coming soon!`);
        }
    });
});
</script>

<?php include 'inc/footer.php'; ?>
