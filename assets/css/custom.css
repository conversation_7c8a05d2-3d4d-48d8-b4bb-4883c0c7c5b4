:root {
    --primary-green: #0C8040;
    --light-green: #029b47;
    --dark-bg: #2c2c2c;
    /* --sidebar-bg: #0C8040; */
}

body {
    background-color: #f8f9fa;
    font-family: 'Poppins', sans-serif;
}

.sidebar {
    background-color: var(--primary-green);
    min-height: 100vh;
    width: 250px;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 1000;
}

.sidebar .logo {
    padding: 25px 20px;
    /* border-bottom: 1px solid rgba(255,255,255,0.1); */
    background-color: #042613;
}

.sidebar .nav-link {
    color: rgba(255,255,255,0.8);
    padding: 15px 20px;
    border: none;
    display: flex;
    align-items: center;
    gap: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s linear;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background-color: rgba(0,0,0,0.1);
    color: white;
}

.main-content {
    margin-left: 250px;
    padding: 0;
}

.top-header {
    /* background-color: var(--dark-bg); */
    /* color: white; */
    padding: 15px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ddd;
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 30px;
    background-color: var(--primary-green);
    padding: 10px 25px;
    border-radius: 75px;
    color: white;
    line-height: 1.1em;
    font-size: 15px;
}
.user-profile strong {
    font-size: 17px;
    font-weight: 600;
}

.user-avatar {
    width: 42px;
    height: 42px;
    border-radius: 50%;
    background-color: #ddd;
}

.content-area {
    padding: 30px;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
    display: flex;
    align-items: center;
    gap: 12px;
}

.section-icon {
    width: 24px;
    height: 24px;
    filter: brightness(0) saturate(100%) invert(20%) sepia(8%) saturate(1000%) hue-rotate(180deg) brightness(95%) contrast(85%);
}

.table-cell-icon {
    width: 18px;
    height: 18px;
    margin-right: 8px;
    color: #666;
    vertical-align: middle;
    margin-top: -2px
}

.table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 70px;
    overflow: hidden;
}


.table td {
    border: none;
    padding: 15px;
    vertical-align: middle;
}

.table > :not(caption) > * > * {
    background-color: transparent !important;
    box-shadow: none !important;
}

.table>thead {
    border-bottom: 1px solid #ddd;
}
.table th {
    background-color: #f8f9fa !important;
    border: none;
    font-weight: 600;
    color: #000;
    padding: 15px;
}

.table-hover tbody tr {
    transition: all 0.3s linear !important; /* Adjust duration and timing function as needed */
    border-bottom: 1px solid #eaeaea;
}
.table-hover tbody tr:last-child {
    border-bottom: none;
}

.table-hover tbody tr:hover {
    background-color: #f0f0f0 !important; /* Choose your desired hover background color */
}


.guide-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.guide-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #ddd;
}

.btn {
    font-size: 15px;
}
.btn-view-details {
    background-color: transparent;
    border: 2px solid var(--light-green);
    color: var(--light-green);
    padding: 8px 20px;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s linear;
}

.btn-view-details:hover {
    background-color: var(--light-green);
    color: white;
}

.btn-view-details img {
    transition: filter 0.3s linear;
}
.btn-view-details:hover img {
    filter: brightness(0) invert(1);
}

.btn-pay-now {
    background-color: var(--primary-green);
    border: none;
    color: white;
    padding: 8px 20px;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s linear;
}
.btn-pay-now:hover {
    background-color: var(--light-green);
    color: white;
}

.payment-info {
    font-size: 1rem;
}

.payment-paid {
    color: var(--light-green);
    font-weight: 600;
}

.payment-due {
    color: #D10000;
    font-weight: 600;
}

.status-valid {
    color: var(--light-green);
    font-weight: 500;
}

/* Booking Status Badges */
.badge {
    font-size: 0.8rem;
    font-weight: 500;
    padding: 6px 12px;
    border-radius: 20px;
}

.badge.bg-success {
    background-color: var(--light-green) !important;
}

.badge.bg-primary {
    background-color: #0066cc !important;
}

.badge.bg-secondary {
    background-color: #6c757d !important;
}

.badge.bg-danger {
    background-color: #dc3545 !important;
}

.badge.bg-warning {
    background-color: #ffc107 !important;
    color: #000 !important;
}

/* Payment Summary Cards */
.payment-summary-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;
    transition: all 0.3s linear;
    border-left: 4px solid var(--primary-green);
}

.payment-summary-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.payment-summary-card.pending {
    border-left-color: #ffc107;
}

.payment-summary-card.refunded {
    border-left-color: #6c757d;
}

.payment-summary-card.total {
    border-left-color: #0066cc;
}

.payment-summary-icon {
    width: 50px;
    height: 50px;
    background-color: var(--primary-green);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.payment-summary-card.pending .payment-summary-icon {
    background-color: #ffc107;
}

.payment-summary-card.refunded .payment-summary-icon {
    background-color: #6c757d;
}

.payment-summary-card.total .payment-summary-icon {
    background-color: #0066cc;
}

.summary-icon {
    width: 24px;
    height: 24px;
    filter: brightness(0) invert(1);
}

.payment-summary-content h4 {
    font-size: 1.8rem;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.payment-summary-content p {
    color: #666;
    margin: 0;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Payment Method Select */
.payment-method-select {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 0.9rem;
    background-color: #f8f9fa;
    min-width: 140px;
}

.payment-method-select:focus {
    border-color: var(--primary-green);
    box-shadow: 0 0 0 0.2rem rgba(12, 128, 64, 0.25);
}

/* Download Receipt Button */
.btn-download-receipt {
    background-color: transparent;
    border: 1px solid #e2e8f0;
    padding: 8px 12px;
    border-radius: 8px;
    transition: all 0.3s linear;
}

.btn-download-receipt:hover {
    background-color: var(--primary-green);
    border-color: var(--primary-green);
}

.btn-download-receipt:hover img {
    filter: brightness(0) invert(1);
}

.btn-download-receipt img {
    width: 16px;
    height: 16px;
    transition: filter 0.3s linear;
}

/* Payment Status Colors */
.payment-refunded {
    color: #6c757d;
    font-weight: 600;
}

/* Receipt Page Styles */
.receipt-container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
    overflow: hidden;
}

.receipt-header {
    /* background: linear-gradient(135deg, var(--primary-green), var(--light-green)); */
    background-color: #042613;
    color: white;
    padding: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.receipt-logo-img {
    height: 50px;
    /* filter: brightness(0) invert(1); */
}

.receipt-title h2 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 600;
}

.receipt-title p {
    margin: 5px 0 0 0;
    opacity: 0.9;
    font-size: 1rem;
}

.receipt-badge {
    font-size: 1rem;
    padding: 8px 16px;
    border-radius: 25px;
}

.receipt-content {
    padding: 40px;
}

.receipt-section {
    margin-bottom: 30px;
    padding: 25px;
    background-color: #f8f9fa;
    border-radius: 12px;
    border-left: 4px solid var(--primary-green);
}

.receipt-section-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.receipt-section-icon {
    width: 20px;
    height: 20px;
    filter: brightness(0) saturate(100%) invert(20%) sepia(8%) saturate(1000%) hue-rotate(180deg) brightness(95%) contrast(85%);
}

.receipt-info p {
    margin: 8px 0;
    color: #555;
    line-height: 1.5;
}

.receipt-info strong {
    color: #333;
    font-weight: 600;
}

.receipt-amount {
    text-align: center;
    padding: 20px;
    background: white;
    border-radius: 12px;
    border: 2px solid var(--primary-green);
}

.receipt-amount h3 {
    color: var(--primary-green);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.amount-display {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-green);
    margin: 0;
}

.receipt-footer {
    border-top: 1px solid #e2e8f0;
    padding-top: 30px;
    margin-top: 20px;
}

.receipt-actions {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.btn-print {
    background-color: var(--primary-green);
    border: none;
    color: white;
    padding: 12px 25px;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s linear;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-print:hover {
    background-color: var(--light-green);
    color: white;
}

.btn-print img {
    width: 16px;
    height: 16px;
    filter: brightness(0) invert(1);
}

.btn-back {
    background-color: transparent;
    border: 2px solid var(--primary-green);
    color: var(--primary-green);
    padding: 10px 25px;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s linear;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-back:hover {
    background-color: var(--primary-green);
    color: white;
    text-decoration: none;
}

.btn-back img {
    filter: brightness(0) saturate(100%) invert(40%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(95%) contrast(85%);
    width: 16px;
    height: 16px;
    transition: filter 0.3s linear;
}

.btn-back:hover img {
    filter: brightness(0) invert(1);
}

.receipt-note {
    text-align: center;
    color: #666;
}

.receipt-note p {
    margin: 5px 0;
    font-size: 0.9rem;
}

.receipt-note small {
    color: #999;
    font-size: 0.8rem;
}

/* Print Styles */
@media print {
    .receipt-container {
        box-shadow: none;
        border: 1px solid #ddd;
    }

    .receipt-actions {
        display: none;
    }

    .breadcrumb-container {
        display: none;
    }

    .sidebar {
        display: none;
    }

    .main-content {
        margin-left: 0;
    }

    .top-header {
        display: none;
    }
}

/* Ongoing Tours Page Styles */
.ongoing-tours-container {
    max-width: 1200px;
    margin: 0 auto;
}

.tour-header {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.tour-info {
    display: flex;
    align-items: center;
    gap: 20px;
}

.tour-icon {
    width: 60px;
    height: 60px;
    /* background-color: var(--primary-green); */
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tour-icon-img {
    width: 56px;
    height: 56px;
    /* filter: brightness(0) invert(1); */
}

.tour-title {
    font-size: 2rem;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.tour-duration {
    color: #666;
    margin: 0;
    font-size: 1rem;
}

.tour-guide-card {
    background: #3E4A5A;
    border-radius: 12px;
    padding: 20px;
    color: white;
}

.guide-header {
    text-align: right;
    color: #a0aec0;
    font-size: 0.9rem;
    margin-bottom: 0px;
}

.guide-info-card {
    display: flex;
    align-items: center;
    gap: 15px;
}

.guide-avatar-large {
    width: 84px;
    height: 84px;
    border-radius: 50%;
    overflow: hidden;
}

.guide-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.guide-name {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
}

.guide-phone {
    color: #a0aec0;
    margin: 3px 0 0 0;
    font-size: 0.9rem;
}

/* Progress Steps */
.tour-progress {
    display: flex;
    justify-content: center;
    margin: 50px 0;
}

.progress-container {
    display: flex;
    align-items: center;
    gap: 0;
}

.progress-step {
    position: relative;
}

.progress-step:not(.active)::after {
    content: '';
    position: absolute;
    top: 5px;
    right: 5px;
    width: 24px;
    height: 24px;
    background-image: url('../img/ico/ico-locked.svg');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.step-circle {
    width: 150px;
    height: 120px;
    border-radius: 12px;
    background-color: #ECEEE4;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #666;
    font-weight: 500;
    position: relative;
    cursor: pointer;
    transition: all 0.3s linear;
}

.progress-step.active .step-circle {
    background-color: var(--primary-green);
    color: white;
}

.step-date {
    font-size: 0.8rem;
    margin-bottom: 0px;
    font-weight: 100;
}

.step-label {
    font-size: 1.6rem;
    font-weight: 600;
}

.progress-line {
    width: 48px;
    height: 48px;
    background-image: url('../img/ico/ico-next.svg');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    margin: 0 15px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Day Details */
.day-details {
    background: white;
    border-radius: 12px;
    padding: 50px 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.day-content {
    max-width: 800px;
    margin: 0 auto;
}

.day-section {
    display: none;
}

.day-section.active {
    display: block;
}

.day-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.day-title {
    font-size: 1.8rem;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.download-link {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #666;
}

.download-btn {
    color: var(--primary-green);
    text-decoration: none;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
    padding: 12px 20px;
    border-radius: 8px;
    background-color: transparent;
    transition: all 0.3s linear;
    min-width: 220px;
}

.download-btn:hover {
    color: white;
    background-color: var(--primary-green);
}

.download-btn:hover img {
    filter: brightness(0) invert(1);
}

.download-btn .btn-text {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    line-height: 1.2;
}

.download-btn .btn-text span {
    font-size: 0.9rem;
    font-weight: 400;
}

.download-btn .btn-text strong {
    font-size: 1rem;
    font-weight: 600;
}

.download-btn .btn-text {
    white-space: nowrap;
}

.download-btn img {
    width: 32px;
    height: 32px;
    transition: filter 0.3s linear;
}

/* Checklist */
.checklist {
    margin-bottom: 30px;
}

.checklist-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 12px 0;
    border-bottom: 1px solid #f1f5f9;
}

.checklist-item:last-child {
    border-bottom: none;
}

.checklist-checkbox {
    width: 20px;
    height: 20px;
    accent-color: var(--primary-green);
}

.checklist-label {
    font-size: 1rem;
    color: #333;
    margin: 0;
    cursor: pointer;
}

/* Comments */
.comments-section {
    margin-bottom: 30px;
}

.comments-textarea {
    background-color: #f8f9fa;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 15px;
    font-size: 1rem;
    resize: vertical;
}

.comments-textarea:focus {
    border-color: var(--primary-green);
    box-shadow: 0 0 0 0.2rem rgba(12, 128, 64, 0.25);
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 15px;
}

.btn-submit {
    background-color: var(--primary-green);
    border: none;
    color: white;
    padding: 12px 30px;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s linear;
}

.btn-submit:hover {
    background-color: var(--light-green);
    color: white;
}

.btn-complaint {
    background-color: #363636;
    border: none;
    color: white;
    padding: 12px 30px;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s linear;
}

.btn-complaint:hover {
    background-color: rgb(29, 30, 31);
    color: white;
}

/* Alert Modal Styles */
.alert-modal {
    background-color: var(--primary-green);
    border: none;
    border-radius: 20px;
    color: white;
    padding: 40px 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.alert-modal .modal-body {
    padding: 0;
}

.alert-icon {
    display: flex;
    justify-content: center;
    align-items: center;
}

.alert-icon-img {
    width: 60px;
    height: 60px;
    filter: brightness(0) invert(1);
}

.alert-title {
    font-size: 2.3rem;
    font-weight: 600;
    color: white;
    margin-bottom: 5px;
}

.alert-message {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.2;
    margin: 0;
    font-weight: 300;
}

.alert-message strong {
    color: white;
    font-weight: 600;
}

/* Breadcrumb Styles */
.breadcrumb-container {
    margin-top: -15px;
    margin-bottom: 40px;
}
.breadcrumb {
    background-color: transparent;
    padding: 0;
    margin: 0;
    font-size: 0.95rem;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    color: #6c757d;
    margin: 0 10px;
    font-weight: 300;
}

.breadcrumb-item a {
    color: var(--dark-bg);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: color 0.3s ease;
}

.breadcrumb-item a:hover {
    color: var(--light-green);
    text-decoration: none;
}

.breadcrumb-item.active {
    color: #6c757d;
    display: flex;
    align-items: center;
    gap: 8px;
}

.breadcrumb-icon {
    width: 16px;
    height: 16px;
    filter: brightness(0) saturate(100%) invert(20%) sepia(8%) saturate(1000%) hue-rotate(180deg) brightness(95%) contrast(85%);
}

.breadcrumb-item a .breadcrumb-icon {
    filter: brightness(0) saturate(100%) invert(40%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(95%) contrast(85%);
}

.breadcrumb-item a:hover .breadcrumb-icon {
    filter: brightness(0) saturate(100%) invert(40%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(95%) contrast(85%);
}

/* Modal backdrop */
.modal-backdrop {
    background-color: rgba(0, 0, 0, 0.7);
}

/* Prevent modal from closing when clicking outside */
.modal.fade .modal-dialog {
    transition: transform 0.3s linear-out;
}

.modal.show .modal-dialog {
    transform: none;
}
